/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 */
define(['N/query', 'N/record', 'N/runtime'],
    /**
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 */
    (query, record, runtime) => {

        const afterSubmit = (scriptContext) => {
            try {
                // Get sales order id
                if(scriptContext.type == scriptContext.UserEventType.CREATE || scriptContext.type == scriptContext.UserEventType.EDIT) {
                    const int_SalesOrderID = scriptContext.newRecord.id;
                    log.debug('int_SalesOrderID', int_SalesOrderID);

                    if(!int_SalesOrderID) return;

                    let qlQuery = /*sql*/`
                        SELECT
                            etail_ord.custrecord_celigo_etail_trans_gateway,
                            etail_ord.custrecord_celigo_etail_trans_amount
                        FROM
                            CUSTOMRECORD_CELIGO_ETAIL_ODR_TRNSACTION etail_ord
                        WHERE
                            etail_ord.custrecord_celigo_etail_orderid = ? AND
                            etail_ord.custrecord_celigo_etail_trans_gateway = 'shop_cash'
                    `;
                    let arr_Results = query.runSuiteQL({
                        query: qlQuery,
                        params: [int_SalesOrderID]
                    }).asMappedResults();

                    if(!arr_Results) return;

                    // get item from parameters
                    const int_ShopCashId = runtime.getCurrentScript().getParameter({ name: 'custscript_blush_shop_cash_id' });

                    // check first if there is already a line item with the same item
                    const int_LineNo = scriptContext.newRecord.findSublistLineWithValue({
                        sublistId: 'item',
                        fieldId: 'item',
                        value: int_ShopCashId
                    });

                    if(int_LineNo != -1) return;

                    // add to item sublist
                    const obj_SalesOrder = record.load({
                        id: int_SalesOrderID,
                        type: record.Type.SALES_ORDER,
                        isDynamic: true
                    });

                    obj_SalesOrder.selectNewLine({
                        sublistId: 'item'
                    });
                    obj_SalesOrder.setCurrentSublistValue({
                        sublistId: 'item',
                        fieldId: 'item',
                        value: int_ShopCashId
                    });

                    obj_SalesOrder.setCurrentSublistValue({
                        sublistId: 'item',
                        fieldId: 'quantity',
                        value: 1
                    });
                    obj_SalesOrder.setCurrentSublistValue({
                        sublistId: 'item',
                        fieldId: 'rate',
                        value: -Math.abs(arr_Results[0].custrecord_celigo_etail_trans_amount)
                    });
                    obj_SalesOrder.commitLine({
                        sublistId: 'item'
                    });

                    const int_SavedSOID = obj_SalesOrder.save();

                    log.debug('int_SavedSOID', `Saved: ${int_SavedSOID}`);

                }

            } catch (err) {
                log.error('error', err);
            }
        }

        return {afterSubmit}

    });
