/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 * 
 * Phillips Collection API Integration - Map Reduce Script
 * Fetches item records from Phillips Collection API for NetSuite integration
 */

define(['N/https', 'N/cache', 'N/log', 'N/runtime', 'N/error'], 
    (https, cache, log, runtime, error) => {

    // Configuration constants
    const CONFIG = {
        API_BASE_URL: 'https://step-up-production.ue.r.appspot.com',
        CACHE_NAME: 'phillips_collection_token_cache',
        CACHE_KEY: 'phillips_access_token',
        CACHE_TTL: 3600, // 1 hour in seconds
        SCRIPT_PARAMS: {
            LAST_SYNC_TIME: 'custscript_phillips_last_sync_time'
        }
    };

    /**
     * Get input data for the map reduce process
     * Fetches ALL item data upfront and returns individual items for processing
     */
    function getInputData() {
        const logTitle = 'getInputData';
        log.debug(logTitle, 'Starting Phillips Collection API data retrieval');

        try {
            // Get script parameters
            const script = runtime.getCurrentScript();
            const lastSyncTime = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.LAST_SYNC_TIME });

            // Use secret ID directly for API key
            const str_ApiKeyID = 'custsecret_phillips_api_key';

            // Get access token
            const accessToken = getAccessToken();
            if (!accessToken) {
                throw error.create({
                    name: 'AUTHENTICATION_ERROR',
                    message: 'Failed to obtain access token'
                });
            }

            // Fetch items using pagination - LIMITED TO 10 ITEMS FOR TESTING
            const allItems = [];
            let currentPage = 1;
            let hasMoreData = true;
            const pageSize = 10; // Use small page size for testing
            const maxItemsForTest = 10; // TESTING LIMIT: Only process 10 items

            log.debug(logTitle, `TESTING MODE: Limiting to ${maxItemsForTest} items only`);

            while (hasMoreData && allItems.length < maxItemsForTest) {
                log.debug(logTitle, `Fetching page ${currentPage} with page size ${pageSize}`);

                // Build URL with pagination and optional filtering
                let url = `${CONFIG.API_BASE_URL}/v1/ecomm/items?page=${currentPage}&page_size=${pageSize}`;

                // Add filter for last sync time if provided
                if (lastSyncTime) {
                    url += `&f=audttime ge ${lastSyncTime}`;
                }

                // Make API call
                const response = https.get({
                    url: url,
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'x-api-key': `{${str_ApiKeyID}}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.code !== 200) {
                    throw error.create({
                        name: 'API_ERROR',
                        message: `Failed to fetch items from page ${currentPage}: ${response.body}`
                    });
                }

                const responseData = JSON.parse(response.body);

                if (responseData.status !== 200) {
                    throw error.create({
                        name: 'API_ERROR',
                        message: `API returned error for page ${currentPage}: ${responseData.message}`
                    });
                }

                const itemsInPage = responseData.data || [];
                const itemCount = itemsInPage.length;

                log.debug(logTitle, `Page ${currentPage} returned ${itemCount} items`);

                // Add items to our collection, but respect the testing limit
                const remainingSlots = maxItemsForTest - allItems.length;
                const itemsToAdd = itemsInPage.slice(0, remainingSlots);
                allItems.push(...itemsToAdd);

                log.debug(logTitle, `Added ${itemsToAdd.length} items, total now: ${allItems.length}`);

                // Check if we've reached our testing limit or end of data
                if (allItems.length >= maxItemsForTest) {
                    hasMoreData = false;
                    log.debug(logTitle, `TESTING LIMIT REACHED: Collected ${allItems.length} items`);
                } else if (itemCount < pageSize) {
                    hasMoreData = false;
                    log.debug(logTitle, `Reached end of data at page ${currentPage} (${itemCount} < ${pageSize})`);
                } else {
                    currentPage++;

                    // Safety check to prevent infinite loops
                    if (currentPage > 100) {
                        log.error(logTitle, 'Reached maximum page limit (100) while fetching items');
                        hasMoreData = false;
                    }
                }
            }

            log.audit(logTitle, `Successfully fetched ${allItems.length} items across ${currentPage} pages`);

            // Return individual items for map stage processing
            return allItems;

        } catch (e) {
            log.error(logTitle, `Error in getInputData: ${e.message}`);
            throw e;
        }
    }

    /**
     * Map stage - Process individual items (data already fetched in getInputData)
     */
    function map(context) {
        const logTitle = 'map';

        try {
            // Parse the individual item from getInputData
            const item = JSON.parse(context.value);
            const itemId = item._id;

            log.debug(logTitle, `Processing item: ${itemId}`);

            // Get access token for enrichment API calls
            const accessToken = getAccessToken();
            if (!accessToken) {
                throw error.create({
                    name: 'AUTHENTICATION_ERROR',
                    message: 'Failed to obtain access token in map stage'
                });
            }

            // Enrich item data with additional details (inventory, pricing)
            const enrichedItem = enrichItemData(item, accessToken);

            // Write enriched item to next stage
            context.write({
                key: itemId,
                value: enrichedItem
            });

            log.debug(logTitle, `Successfully processed item: ${itemId} - ${item.desc || 'No description'}`);

        } catch (e) {
            log.error(logTitle, `Error in map stage: ${e.message}`);
            throw e;
        }
    }

    /**
     * Reduce stage - Process individual items (optional for this use case)
     */
    function reduce(context) {
        const logTitle = 'reduce';
        
        try {
            const itemId = context.key;
            const itemData = JSON.parse(context.values[0]);

            log.debug(logTitle, `Processing item: ${itemId}`);

            // Here you would typically save to NetSuite
            // For now, we'll just log the processed item
            log.audit(logTitle, `Item processed: ${itemId} - ${itemData.desc}`);

            context.write({
                key: itemId,
                value: {
                    status: 'processed',
                    itemId: itemId,
                    description: itemData.desc
                }
            });

        } catch (e) {
            log.error(logTitle, `Error in reduce stage for item ${context.key}: ${e.message}`);
            throw e;
        }
    }

    /**
     * Summarize stage - Handle completion and errors
     */
    function summarize(summary) {
        const logTitle = 'summarize';
        
        log.audit(logTitle, 'Phillips Collection API sync completed');
        log.audit(logTitle, `Input stage: ${summary.inputSummary.seconds} seconds`);
        log.audit(logTitle, `Map stage: ${summary.mapSummary.seconds} seconds, ${summary.mapSummary.keys} items processed`);
        log.audit(logTitle, `Reduce stage: ${summary.reduceSummary.seconds} seconds, ${summary.reduceSummary.keys} items reduced`);

        // Handle errors
        handleStageErrors('Input', summary.inputSummary);
        handleStageErrors('Map', summary.mapSummary);
        handleStageErrors('Reduce', summary.reduceSummary);

        // Log successful processing
        let processedCount = 0;
        summary.output.iterator().each(() => {
            processedCount++;
            return true;
        });

        log.audit(logTitle, `Successfully processed ${processedCount} items`);
    }

    /**
     * Get cached access token or fetch new one
     */
    function getAccessToken() {
        const logTitle = 'getAccessToken';
        
        try {
            const tokenCache = cache.getCache({
                name: CONFIG.CACHE_NAME,
                scope: cache.Scope.PRIVATE
            });

            const accessToken = tokenCache.get({
                key: CONFIG.CACHE_KEY,
                loader: fetchNewAccessToken,
                ttl: CONFIG.CACHE_TTL
            });

            return accessToken;

        } catch (e) {
            log.error(logTitle, `Error getting access token: ${e.message}`);
            return null;
        }
    }

    /**
     * Fetch new access token from Phillips Collection API
     */
    function fetchNewAccessToken() {
        const logTitle = 'fetchNewAccessToken';

        try {
            // Use secret IDs directly like in USPS scripts
            const str_UsernameID = 'custsecret_phillips_api_username';
            const str_PasswordID = 'custsecret_phillips_api_password';
            const str_ApiKeyID = 'custsecret_phillips_api_key';

            const authPayload = {
                email: `{${str_UsernameID}}`,
                password: `{${str_PasswordID}}`
            };

            const response = https.post({
                url: `${CONFIG.API_BASE_URL}/v1/auth`,
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': `{${str_ApiKeyID}}`
                },
                body: JSON.stringify(authPayload)
            });

            if (response.code !== 200) {
                throw error.create({
                    name: 'AUTH_FAILED',
                    message: `Authentication failed: ${response.body}`
                });
            }

            const authResponse = JSON.parse(response.body);

            if (authResponse.status !== 200 || !authResponse.data || !authResponse.data.token) {
                throw error.create({
                    name: 'AUTH_FAILED',
                    message: 'Invalid authentication response'
                });
            }

            log.debug(logTitle, 'Successfully obtained new access token');
            return authResponse.data.token;

        } catch (e) {
            log.error(logTitle, `Error fetching access token: ${e.message}`);
            throw e;
        }
    }





    /**
     * Enrich item data with additional details (inventory, pricing, etc.)
     */
    function enrichItemData(item, accessToken) {
        const logTitle = 'enrichItemData';
        
        try {
            // Use secret ID directly for API key
            const str_ApiKeyID = 'custsecret_phillips_api_key';

            const enrichedItem = { ...item };

            // Get inventory data
            try {
                const inventoryResponse = https.get({
                    url: `${CONFIG.API_BASE_URL}/v1/ecomm/items/${item._id}/inventory`,
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'x-api-key': `{${str_ApiKeyID}}`,
                        'Accept': 'application/json'
                    }
                });

                if (inventoryResponse.code === 200) {
                    const inventoryData = JSON.parse(inventoryResponse.body);
                    if (inventoryData.status === 200) {
                        enrichedItem.inventory = inventoryData.data;
                    }
                }
            } catch (invError) {
                log.debug(logTitle, `Could not get inventory for item ${item._id}: ${invError.message}`);
            }

            // Get pricing data
            try {
                const priceResponse = https.get({
                    url: `${CONFIG.API_BASE_URL}/v1/ecomm/items/${item._id}/price`,
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'x-api-key': `{${str_ApiKeyID}}`,
                        'Accept': 'application/json'
                    }
                });

                if (priceResponse.code === 200) {
                    const priceData = JSON.parse(priceResponse.body);
                    if (priceData.status === 200) {
                        enrichedItem.pricing = priceData.data;
                    }
                }
            } catch (priceError) {
                log.debug(logTitle, `Could not get pricing for item ${item._id}: ${priceError.message}`);
            }

            return enrichedItem;

        } catch (e) {
            log.error(logTitle, `Error enriching item data for ${item._id}: ${e.message}`);
            return item; // Return original item if enrichment fails
        }
    }

    /**
     * Handle errors from different stages
     */
    function handleStageErrors(stageName, stageSummary) {
        if (stageSummary.errors && stageSummary.errors.iterator) {
            stageSummary.errors.iterator().each((key, errorMsg) => {
                log.error(`${stageName} Stage Error`, `Key: ${key}, Error: ${errorMsg}`);
                return true;
            });
        }
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    };
});
