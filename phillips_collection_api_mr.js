/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NModuleScope SameAccount
 * 
 * Phillips Collection API Integration - Map Reduce Script
 * Fetches item records from Phillips Collection API for NetSuite integration
 */

define(['N/https', 'N/cache', 'N/log', 'N/runtime', 'N/error'], 
    (https, cache, log, runtime, error) => {

    // Configuration constants
    const CONFIG = {
        API_BASE_URL: 'https://api.phillipscollection.com', // Replace with actual base URL
        CACHE_NAME: 'phillips_collection_token_cache',
        CACHE_KEY: 'phillips_access_token',
        CACHE_TTL: 3600, // 1 hour in seconds
        PAGE_SIZE: 50,
        SCRIPT_PARAMS: {
            API_USERNAME: 'custscript_phillips_api_username',
            API_PASSWORD: 'custscript_phillips_api_password',
            API_KEY: 'custscript_phillips_api_key',
            LAST_SYNC_TIME: 'custscript_phillips_last_sync_time'
        }
    };

    /**
     * Get input data for the map reduce process
     * Returns array of page numbers to process
     */
    function getInputData() {
        const logTitle = 'getInputData';
        log.debug(logTitle, 'Starting Phillips Collection API data retrieval');

        try {
            // Get script parameters
            const script = runtime.getCurrentScript();
            const lastSyncTime = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.LAST_SYNC_TIME });
            
            // Get access token
            const accessToken = getAccessToken();
            if (!accessToken) {
                throw error.create({
                    name: 'AUTHENTICATION_ERROR',
                    message: 'Failed to obtain access token'
                });
            }

            // Get total count of items to determine pagination
            const totalItems = getTotalItemCount(accessToken, lastSyncTime);
            const totalPages = Math.ceil(totalItems / CONFIG.PAGE_SIZE);
            
            log.debug(logTitle, `Total items: ${totalItems}, Total pages: ${totalPages}`);

            // Return array of page numbers for processing
            const pages = [];
            for (let i = 1; i <= totalPages; i++) {
                pages.push({
                    page: i,
                    lastSyncTime: lastSyncTime
                });
            }

            return pages;

        } catch (e) {
            log.error(logTitle, `Error in getInputData: ${e.message}`);
            throw e;
        }
    }

    /**
     * Map stage - Process each page of items
     */
    function map(context) {
        const logTitle = 'map';
        
        try {
            const inputData = JSON.parse(context.value);
            const pageNumber = inputData.page;
            const lastSyncTime = inputData.lastSyncTime;

            log.debug(logTitle, `Processing page ${pageNumber}`);

            // Get access token
            const accessToken = getAccessToken();
            if (!accessToken) {
                throw error.create({
                    name: 'AUTHENTICATION_ERROR',
                    message: 'Failed to obtain access token in map stage'
                });
            }

            // Fetch items for this page
            const items = getItemsPage(accessToken, pageNumber, lastSyncTime);
            
            // Process each item
            items.forEach((item, index) => {
                try {
                    // Enrich item data with additional details
                    const enrichedItem = enrichItemData(item, accessToken);
                    
                    context.write({
                        key: item._id,
                        value: enrichedItem
                    });

                } catch (itemError) {
                    log.error(logTitle, `Error processing item ${item._id}: ${itemError.message}`);
                    // Continue processing other items
                }
            });

            log.debug(logTitle, `Completed processing page ${pageNumber} with ${items.length} items`);

        } catch (e) {
            log.error(logTitle, `Error in map stage: ${e.message}`);
            throw e;
        }
    }

    /**
     * Reduce stage - Process individual items (optional for this use case)
     */
    function reduce(context) {
        const logTitle = 'reduce';
        
        try {
            const itemId = context.key;
            const itemData = JSON.parse(context.values[0]);

            log.debug(logTitle, `Processing item: ${itemId}`);

            // Here you would typically save to NetSuite
            // For now, we'll just log the processed item
            log.audit(logTitle, `Item processed: ${itemId} - ${itemData.desc}`);

            context.write({
                key: itemId,
                value: {
                    status: 'processed',
                    itemId: itemId,
                    description: itemData.desc
                }
            });

        } catch (e) {
            log.error(logTitle, `Error in reduce stage for item ${context.key}: ${e.message}`);
            throw e;
        }
    }

    /**
     * Summarize stage - Handle completion and errors
     */
    function summarize(summary) {
        const logTitle = 'summarize';
        
        log.audit(logTitle, 'Phillips Collection API sync completed');
        log.audit(logTitle, `Input stage: ${summary.inputSummary.seconds} seconds`);
        log.audit(logTitle, `Map stage: ${summary.mapSummary.seconds} seconds, ${summary.mapSummary.keys} items processed`);
        log.audit(logTitle, `Reduce stage: ${summary.reduceSummary.seconds} seconds, ${summary.reduceSummary.keys} items reduced`);

        // Handle errors
        handleStageErrors('Input', summary.inputSummary);
        handleStageErrors('Map', summary.mapSummary);
        handleStageErrors('Reduce', summary.reduceSummary);

        // Log successful processing
        let processedCount = 0;
        summary.output.iterator().each((key, value) => {
            processedCount++;
            return true;
        });

        log.audit(logTitle, `Successfully processed ${processedCount} items`);
    }

    /**
     * Get cached access token or fetch new one
     */
    function getAccessToken() {
        const logTitle = 'getAccessToken';
        
        try {
            const tokenCache = cache.getCache({
                name: CONFIG.CACHE_NAME,
                scope: cache.Scope.PRIVATE
            });

            const accessToken = tokenCache.get({
                key: CONFIG.CACHE_KEY,
                loader: fetchNewAccessToken,
                ttl: CONFIG.CACHE_TTL
            });

            return accessToken;

        } catch (e) {
            log.error(logTitle, `Error getting access token: ${e.message}`);
            return null;
        }
    }

    /**
     * Fetch new access token from Phillips Collection API
     */
    function fetchNewAccessToken() {
        const logTitle = 'fetchNewAccessToken';
        
        try {
            const script = runtime.getCurrentScript();
            const username = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.API_USERNAME });
            const password = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.API_PASSWORD });
            const apiKey = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.API_KEY });

            if (!username || !password || !apiKey) {
                throw error.create({
                    name: 'MISSING_CREDENTIALS',
                    message: 'API credentials not configured'
                });
            }

            const authPayload = {
                email: username,
                password: password
            };

            const response = https.post({
                url: `${CONFIG.API_BASE_URL}/v1/auth`,
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': apiKey
                },
                body: JSON.stringify(authPayload)
            });

            if (response.code !== 200) {
                throw error.create({
                    name: 'AUTH_FAILED',
                    message: `Authentication failed: ${response.body}`
                });
            }

            const authResponse = JSON.parse(response.body);
            
            if (authResponse.status !== 200 || !authResponse.token) {
                throw error.create({
                    name: 'AUTH_FAILED',
                    message: 'Invalid authentication response'
                });
            }

            log.debug(logTitle, 'Successfully obtained new access token');
            return authResponse.token;

        } catch (e) {
            log.error(logTitle, `Error fetching access token: ${e.message}`);
            throw e;
        }
    }

    /**
     * Get total count of items for pagination
     */
    function getTotalItemCount(accessToken, lastSyncTime) {
        const logTitle = 'getTotalItemCount';
        
        try {
            const script = runtime.getCurrentScript();
            const apiKey = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.API_KEY });

            let url = `${CONFIG.API_BASE_URL}/v1/ecomm/items?page=1&page_size=1`;
            
            // Add filter for last sync time if provided
            if (lastSyncTime) {
                url += `&f=audttime ge ${lastSyncTime}`;
            }

            const response = https.get({
                url: url,
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'x-api-key': apiKey,
                    'Accept': 'application/json'
                }
            });

            if (response.code !== 200) {
                throw error.create({
                    name: 'API_ERROR',
                    message: `Failed to get item count: ${response.body}`
                });
            }

            const responseData = JSON.parse(response.body);
            
            if (responseData.status !== 200) {
                throw error.create({
                    name: 'API_ERROR',
                    message: `API returned error: ${responseData.message}`
                });
            }

            // Note: This assumes the API returns total count in response
            // You may need to adjust based on actual API response structure
            return responseData.total || responseData.data.length || 0;

        } catch (e) {
            log.error(logTitle, `Error getting total item count: ${e.message}`);
            throw e;
        }
    }

    /**
     * Get items for a specific page
     */
    function getItemsPage(accessToken, pageNumber, lastSyncTime) {
        const logTitle = 'getItemsPage';
        
        try {
            const script = runtime.getCurrentScript();
            const apiKey = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.API_KEY });

            let url = `${CONFIG.API_BASE_URL}/v1/ecomm/items?page=${pageNumber}&page_size=${CONFIG.PAGE_SIZE}`;
            
            // Add filter for last sync time if provided
            if (lastSyncTime) {
                url += `&f=audttime ge ${lastSyncTime}`;
            }

            const response = https.get({
                url: url,
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'x-api-key': apiKey,
                    'Accept': 'application/json'
                }
            });

            if (response.code !== 200) {
                throw error.create({
                    name: 'API_ERROR',
                    message: `Failed to get items page ${pageNumber}: ${response.body}`
                });
            }

            const responseData = JSON.parse(response.body);
            
            if (responseData.status !== 200) {
                throw error.create({
                    name: 'API_ERROR',
                    message: `API returned error for page ${pageNumber}: ${responseData.message}`
                });
            }

            return responseData.data || [];

        } catch (e) {
            log.error(logTitle, `Error getting items page ${pageNumber}: ${e.message}`);
            throw e;
        }
    }

    /**
     * Enrich item data with additional details (inventory, pricing, etc.)
     */
    function enrichItemData(item, accessToken) {
        const logTitle = 'enrichItemData';
        
        try {
            const script = runtime.getCurrentScript();
            const apiKey = script.getParameter({ name: CONFIG.SCRIPT_PARAMS.API_KEY });

            const enrichedItem = { ...item };

            // Get inventory data
            try {
                const inventoryResponse = https.get({
                    url: `${CONFIG.API_BASE_URL}/v1/ecomm/items/${item._id}/inventory`,
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'x-api-key': apiKey,
                        'Accept': 'application/json'
                    }
                });

                if (inventoryResponse.code === 200) {
                    const inventoryData = JSON.parse(inventoryResponse.body);
                    if (inventoryData.status === 200) {
                        enrichedItem.inventory = inventoryData.data;
                    }
                }
            } catch (invError) {
                log.debug(logTitle, `Could not get inventory for item ${item._id}: ${invError.message}`);
            }

            // Get pricing data
            try {
                const priceResponse = https.get({
                    url: `${CONFIG.API_BASE_URL}/v1/ecomm/items/${item._id}/price`,
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'x-api-key': apiKey,
                        'Accept': 'application/json'
                    }
                });

                if (priceResponse.code === 200) {
                    const priceData = JSON.parse(priceResponse.body);
                    if (priceData.status === 200) {
                        enrichedItem.pricing = priceData.data;
                    }
                }
            } catch (priceError) {
                log.debug(logTitle, `Could not get pricing for item ${item._id}: ${priceError.message}`);
            }

            return enrichedItem;

        } catch (e) {
            log.error(logTitle, `Error enriching item data for ${item._id}: ${e.message}`);
            return item; // Return original item if enrichment fails
        }
    }

    /**
     * Handle errors from different stages
     */
    function handleStageErrors(stageName, stageSummary) {
        if (stageSummary.errors && stageSummary.errors.iterator) {
            stageSummary.errors.iterator().each((key, errorMsg) => {
                log.error(`${stageName} Stage Error`, `Key: ${key}, Error: ${errorMsg}`);
                return true;
            });
        }
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    };
});
